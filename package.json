{"name": "zotero-pdf2zh", "version": "2.4.3", "description": "Zotero Pdf2zh", "config": {"addonName": "Zotero Pdf2zh", "addonID": "<EMAIL>", "addonRef": "pdf2zh", "addonInstance": "pdf2zh", "prefsPrefix": "extensions.zotero.pdf2zh"}, "repository": {"type": "git", "url": "git+https://github.com/guaguastandup/zotero-pdf2zh.git"}, "author": "guaguastandup", "bugs": {"url": "https://github.com/guaguastandup/zotero-pdf2zh/issues"}, "homepage": "https://github.com/guaguastandup/zotero-pdf2zh#readme", "license": "AGPL-3.0-or-later", "scripts": {"start": "zotero-plugin serve", "build": "tsc --noEmit && zotero-plugin build", "lint:check": "prettier --check . && eslint .", "lint:fix": "prettier --write . && eslint . --fix", "release": "zotero-plugin release", "test": "echo \"Error: no test specified\" && exit 1", "update-deps": "npm update --save"}, "dependencies": {"zotero-plugin-toolkit": "^4.1.2"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.13.10", "axios": "^1.8.2", "eslint": "^9.22.0", "form-data": "^4.0.2", "path": "^0.12.7", "prettier": "^3.5.3", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0", "zotero-plugin-scaffold": "^0.3.2", "zotero-types": "^3.1.7"}, "prettier": {"printWidth": 80, "tabWidth": 4, "endOfLine": "lf", "overrides": [{"files": ["*.xhtml"], "options": {"htmlWhitespaceSensitivity": "css"}}]}}