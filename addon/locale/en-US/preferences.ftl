pref-title = Zotero PDF2zh
pref-enable =
    .label = Enable
pref-input = Input
pref-serverip = Python Server IP

pref-help = { $name } Build { $version } { $time }

pref-mono = generate mono file (Translated language only)
pref-dual = generate dual file (Bilingual translation)
pref-mono-cut = generate single-column mono file
pref-dual-cut = generate single-column dual file
pref-compare = generate compare file (for double column PDF)
pref-single-compare = generate compare file (for single column PDF)

pref-engine = Translation Engine
pref-service = Translation Services
pref-threadNum = Thread Num

pref-outputPath = Translated File Path
pref-configPath = Pdf2zh Config File Path

pref-default-generate = Defualt Generate Files
pref-translate-config = Translation Config

pref-rename = Rename 
pref-babeldoc = Enable Babeldoc (Experimental)
pref-skip-subset-fonts = Skip Subset Fonts(Try it when meet error)
pref-skip-last-pages = Skip the last few pages without translating
pref-open = Open Generated File Automatically

pref-sourceLang = Source Language
pref-targetLang = Target Language
