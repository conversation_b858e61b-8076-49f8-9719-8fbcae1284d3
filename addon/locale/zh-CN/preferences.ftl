pref-title = Zotero PDF2zh
pref-enable =
    .label = 开启
pref-input = 输入
pref-serverip = Python Server IP

pref-help = { $name } Build { $version } { $time }

pref-mono = 生成mono文件 (只有中文)
pref-dual = 生成dual文件 (双语对照)
pref-mono-cut = 生成单栏mono文件 (适配手机阅读)
pref-dual-cut = 生成单栏dual文件 (适配手机阅读)
pref-compare = 生成双语对照文件（双栏PDF, 切割后拼接）
pref-single-compare = 生成双语对照文件（单栏PDF, 不切割, 左右拼接）

pref-engine = 翻译引擎
pref-service = 翻译服务
pref-threadNum = 线程数

pref-outputPath = 翻译文件输出路径(临时路径可删除)
pref-configPath = Pdf2zh配置文件路径(配置翻译引擎&字体)

pref-default-generate = 默认生成文件
pref-translate-config = 翻译配置

pref-rename = 重命名条目为短标题（如‘短标题-dual', 实际文件名不变)
pref-babeldoc = 启用Babeldoc (Experimental)
pref-skip-subset-fonts = 跳过字体子集化(在PDF渲染失败时可以尝试, 生成文件大小更大)
pref-skip-last-pages = 跳过最后几页不翻译
pref-open = 生成后自动打开文件

pref-sourceLang = 源语言
pref-targetLang = 目标语言