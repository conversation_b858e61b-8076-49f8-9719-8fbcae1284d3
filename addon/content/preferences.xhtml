<linkset>
    <html:link rel="localization" href="__addonRef__-preferences.ftl" />
</linkset>

<groupbox onload="Zotero.__addonInstance__.hooks.onPrefsEvent('load', { window });">
    <vbox>
        <!--翻译配置-->
        <html:h3 data-l10n-id="pref-translate-config"></html:h3>
        <hbox align="center">
            <html:label for="zotero-prefpane-__addonRef__-serverip" data-l10n-id="pref-serverip"></html:label>
            <html:input type="text" id="zotero-prefpane-__addonRef__-serverip"
                preference="extensions.zotero.__addonRef__.serverip" style="width: 200px"></html:input>
        </hbox>

        <hbox align="center">
            <html:label for="zotero-prefpane-__addonRef__-sourceLang" data-l10n-id="pref-sourceLang" />
            <html:input type="text" id="zotero-prefpane-__addonRef__-sourceLang" flex="1"
                preference="extensions.zotero.__addonRef__.sourceLang" style="width: 50px; " />
            <html:label for="zotero-prefpane-__addonRef__-targetLang" data-l10n-id="pref-targetLang" />
            <html:input type="text" id="zotero-prefpane-__addonRef__-targetLang" flex="1"
                preference="extensions.zotero.__addonRef__.targetLang" style="width: 50px; " />
        </hbox>

        <hbox align="center">
            <html:label for="zotero-prefpane-__addonRef__-engine" data-l10n-id="pref-engine" />
            <hbox flex="1">
                <html:input type="text" id="zotero-prefpane-__addonRef__-engine" flex="1"
                    preference="extensions.zotero.__addonRef__.engine" style="width: 120px; " />
                <html:select id="zotero-prefpane-__addonRef__-engineselect" style="width: 120px;"
                    preference="extensions.zotero.__addonRef__.engineselect">
                    <html:option value="pdf2zh">pdf2zh</html:option>
                    <html:option value="pdf2zh_next">pdf2zh_next</html:option>
                    <!-- <html:option value="EbookTranslator">EbookTranslator</html:option> -->
                </html:select>
            </hbox>
        </hbox>

        <hbox align="center">
            <html:label for="zotero-prefpane-__addonRef__-service" data-l10n-id="pref-service" />
            <hbox flex="1">
                <html:input type="text" id="zotero-prefpane-__addonRef__-service" flex="1"
                    preference="extensions.zotero.__addonRef__.service" style="width: 120px; " />
                <html:select id="zotero-prefpane-__addonRef__-serviceselect" style="width: 120px;"
                    preference="extensions.zotero.__addonRef__.serviceselect">
                    <html:option value="google">google</html:option>
                    <html:option value="bing">bing</html:option>

                    <html:option value="deepl">deepl</html:option>
                    <html:option value="deeplx">deeplx</html:option>

                    <html:option value="ollama">ollama</html:option>
                    <html:option value="xinference">xinference</html:option>

                    <html:option value="openai">openai</html:option>
                    <html:option value="azure-openai">azure-openai</html:option>
                    <html:option value="zhipu">zhipu</html:option>

                    <html:option value="modelscope">modelscope</html:option>
                    <html:option value="silicon">silicon</html:option>
                    <html:option value="gemini">gemini</html:option>
                    <html:option value="azure">azure</html:option>

                    <html:option value="tencent">tencent</html:option>
                    <html:option value="dify">dify</html:option>
                    <html:option value="anythingllm">anythingllm</html:option>

                    <html:option value="argos">argos</html:option>

                    <html:option value="grok">grok</html:option>
                    <html:option value="groq">groq</html:option>

                    <html:option value="deepseek">deepseek</html:option>

                    <html:option value="openailiked">openailiked</html:option>
                    <html:option value="qwen-mt">qwen-mt</html:option>
                </html:select>
            </hbox>
        </hbox>

        <hbox align=" center">
            <html:label for="zotero-prefpane-__addonRef__-threadNum" data-l10n-id="pref-threadNum"></html:label>
            <html:input type="text" id="zotero-prefpane-__addonRef__-threadNum"
                preference="extensions.zotero.__addonRef__.threadNum" style="width: 20px"></html:input>
        </hbox>
        <vbox align="left">
            <html:label for="zotero-prefpane-__addonRef__-outputPath" data-l10n-id="pref-outputPath"></html:label>
            <html:input type="text" id="zotero-prefpane-__addonRef__-outputPath"
                preference="extensions.zotero.__addonRef__.outputPath" style="width: 300px"></html:input>
        </vbox>
        <vbox align="left">
            <html:label for="zotero-prefpane-__addonRef__-configPath" data-l10n-id="pref-configPath"></html:label>
            <html:input type="text" id="zotero-prefpane-__addonRef__-configPath"
                preference="extensions.zotero.__addonRef__.configPath" style="width: 300px"></html:input>
        </vbox>
        <hbox align="left">
            <html:label for="zotero-prefpane-__addonRef__-skip-last-pages" data-l10n-id="pref-skip-last-pages"></html:label>
            <html:input type="text" id="zotero-prefpane-__addonRef__-skip-last-pages"
                preference="extensions.zotero.__addonRef__.skip-last-pages" style="width: 50px"></html:input>
        </hbox>
        <hbox>
            <checkbox native="true" id="zotero-prefpane-__addonRef__-rename"
                preference="extensions.zotero.__addonRef__.rename" />
            <html:label data-l10n-id="pref-rename" for="zotero-prefpane-__addonRef__-rename" />
        </hbox>
        <hbox>
            <checkbox native="true" id="zotero-prefpane-__addonRef__-skip-subset-fonts"
                preference="extensions.zotero.__addonRef__.skip-subset-fonts" />
            <html:label data-l10n-id="pref-skip-subset-fonts" for="zotero-prefpane-__addonRef__-skip-subset-fonts" />
        </hbox>
        <hbox>
            <checkbox native="true" id="zotero-prefpane-__addonRef__-babeldoc"
                preference="extensions.zotero.__addonRef__.babeldoc" />
            <html:label data-l10n-id="pref-babeldoc" for="zotero-prefpane-__addonRef__-babeldoc" />
        </hbox>
    </vbox>
    <vbox>
        <html:h3 data-l10n-id="pref-default-generate"></html:h3>
        <hbox>
            <checkbox native="true" id="zotero-prefpane-__addonRef__-mono"
                preference="extensions.zotero.__addonRef__.mono" />
            <html:label data-l10n-id="pref-mono" for="zotero-prefpane-__addonRef__-mono" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__-mono-open"
                preference="extensions.zotero.__addonRef__.mono-open" />
            <html:label data-l10n-id="pref-open" for="zotero-prefpane-__addonRef__-mono-open" />
        </hbox>
        <hbox>
            <checkbox native="true" id="zotero-prefpane-__addonRef__-dual"
                preference="extensions.zotero.__addonRef__.dual" />
            <html:label data-l10n-id="pref-dual" for="zotero-prefpane-__addonRef__-dual" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__dual-open"
                preference="extensions.zotero.__addonRef__.dual-open" />
            <html:label data-l10n-id="pref-open" for="zotero-prefpane-__addonRef__-dual-open" />
        </hbox>
        <hbox>
            <checkbox native="false" id="zotero-prefpane-__addonRef__-mono-cut"
                preference="extensions.zotero.__addonRef__.mono-cut" />
            <html:label data-l10n-id="pref-mono-cut" for="zotero-prefpane-__addonRef__-mono-cut" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__-mono-cut-open"
                preference="extensions.zotero.__addonRef__.mono-cut-open" />
            <html:label data-l10n-id="pref-open" for="zotero-prefpane-__addonRef__-mono-cut-open" />
        </hbox>
        <hbox>
            <checkbox native="false" id="zotero-prefpane-__addonRef__-dual-cut"
                preference="extensions.zotero.__addonRef__.dual-cut" />
            <html:label data-l10n-id="pref-dual-cut" for="zotero-prefpane-__addonRef__-dual-cut" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__-dual-cut-open"
                preference="extensions.zotero.__addonRef__.dual-cut-open" />
            <html:label data-l10n-id="pref-open" for="-zotero-prefpane-__addonRef__dual-cut-open" />
        </hbox>
        <hbox>
            <checkbox native="false" id="zotero-prefpane-__addonRef__-compare"
                preference="extensions.zotero.__addonRef__.compare" />
            <html:label data-l10n-id="pref-compare" for="zotero-prefpane-__addonRef__-compare" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__-compare-open"
                preference="extensions.zotero.__addonRef__.compare-open" />
            <html:label data-l10n-id="pref-open" for="zotero-prefpane-__addonRef__-compare-open" />
        </hbox>
        <hbox>
            <checkbox native="false" id="zotero-prefpane-__addonRef__-single-compare"
                preference="extensions.zotero.__addonRef__.single-compare" />
            <html:label data-l10n-id="pref-single-compare" for="zotero-prefpane-__addonRef__-singel-compare" />
            <checkbox native="false" id="zotero-prefpane-__addonRef__-single-compare-open"
                preference="extensions.zotero.__addonRef__.single-compare-open" />
            <html:label data-l10n-id="pref-open" for="zotero-prefpane-__addonRef__-single-compare-open" />
        </hbox>
    </vbox>

    <!-- 说明 -->
    <vbox>
        <html:h3>Python Server 脚本 </html:h3>
        <p>🌷 温馨提示: 本插件需要使用终端或docker执行Python脚本，才可以使用翻译功能。</p>
        <p>🌷 [2025年6月12日v2.4.3] Python脚本已更新，支持pdf2zh_next翻译引擎，请访问主页README.md进行更新:
            <description align="center"><label class="zotero-text-link" value="README.md"
                    href="https://github.com/guaguastandup/zotero-pdf2zh"></label></description>
        </p>
        <p>🌷 如果遇到问题请在github页面提issue, 谢谢! :-)</p>
    </vbox>
</groupbox>

<vbox>
    <html:h2></html:h2>
    <html:label data-l10n-id="pref-help"
        data-l10n-args='{"time": "__buildTime__","name": "__addonName__","version":"__buildVersion__"}'></html:label>
</vbox>