/* eslint-disable no-undef */
pref("__prefsPrefix__.enable", true);
pref("__prefsPrefix__.input", "This is input");
pref("__prefsPrefix__.serverip", "http://localhost:8888");

pref("__prefsPrefix__.service", "bing");
pref("__prefsPrefix__.serviceselect", "bing");
pref("__prefsPrefix__.engine", "pdf2zh");
pref("__prefsPrefix__.engineselect", "pdf2zh");

pref("__prefsPrefix__.mono", true);
pref("__prefsPrefix__.dual", true);
pref("__prefsPrefix__.mono-cut", false);
pref("__prefsPrefix__.dual-cut", false);
pref("__prefsPrefix__.compare", false);
pref("__prefsPrefix__.singel-compare", false);
pref("__prefsPrefix__.threadNum", 4);
pref("__prefsPrefix__.outputPath", "./translated/");
pref("__prefsPrefix__.configPath", "./config.json");

pref("__prefsPrefix__.mono-open", false);
pref("__prefsPrefix__.dual-open", false);
pref("__prefsPrefix__.mono-cut-open", false);
pref("__prefsPrefix__.dual-cut-open", false);
pref("__prefsPrefix__.compare-open", false);
pref("__prefsPrefix__.single-compare-open", false);

pref("__prefsPrefix__.rename", true);
pref("__prefsPrefix__.babeldoc", false);

pref("__prefsPrefix__.sourceLang", "en");
pref("__prefsPrefix__.targetLang", "zh");
pref("__prefsPrefix__.skip-subset-fonts", false);
pref("__prefsPrefix__.skip-last-pages", 0);
