// Generated by zotero-plugin-scaffold
/* prettier-ignore */
/* eslint-disable */
// @ts-nocheck

// prettier-ignore
declare namespace _ZoteroTypes {
  interface Prefs {
    PluginPrefsMap: {
      "enable": boolean;
      "input": string;
      "serverip": string;
      "service": string;
      "serviceselect": string;
      "engine": string;
      "engineselect": string;
      "mono": boolean;
      "dual": boolean;
      "mono-cut": boolean;
      "dual-cut": boolean;
      "compare": boolean;
      "singel-compare": boolean;
      "threadNum": number;
      "outputPath": string;
      "configPath": string;
      "mono-open": boolean;
      "dual-open": boolean;
      "mono-cut-open": boolean;
      "dual-cut-open": boolean;
      "compare-open": boolean;
      "single-compare-open": boolean;
      "rename": boolean;
      "babeldoc": boolean;
      "sourceLang": string;
      "targetLang": string;
      "skip-subset-fonts": boolean;
      "skip-last-pages": number;
    };
  }
}
