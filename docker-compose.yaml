services:
    zotero-pdf2zh:
        build:
            context: .
            dockerfile: Dockerfile
            args:
                - ZOTERO_PDF2ZH_FROM_IMAGE=byaidu/pdf2zh:1.9.6
                - ZOTERO_PDF2ZH_SERVER_FILE_DOWNLOAD_URL=https://raw.githubusercontent.com/guaguastandup/zotero-pdf2zh/refs/heads/main/server.py
                # Cancel comments for domestic packaging
                # - ZOTERO_PDF2ZH_SERVER_FILE_DOWNLOAD_URL=https://raw.gitmirror.com/guaguastandup/zotero-pdf2zh/refs/heads/main/server.py
        container_name: zotero-pdf2zh
        restart: unless-stopped
        ports:
            - 8888:8888
        environment:
            - TZ=Asia/Shanghai
            - HF_ENDPOINT=https://hf-mirror.com
        volumes:
            - ./zotero-pdf2zh/translated:/app/translated
            - ./zotero-pdf2zh/config.json:/app/config.json
            # - ./zotero-pdf2zh/LXGWWenKai-Regular.ttf:/app/LXGWWenKai-Regular.ttf
