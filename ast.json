{"type": "<PERSON><PERSON><PERSON>", "span": {"start": 1891, "end": 10253}, "body": [{"type": "ExpressionStatement", "span": {"start": 1891, "end": 1927}, "expression": {"type": "CallExpression", "span": {"start": 1891, "end": 1926}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 1891, "end": 1900}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 1901, "end": 1918}, "value": "app.update.auto", "raw": "\"app.update.auto\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 1920, "end": 1925}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 1928, "end": 1967}, "expression": {"type": "CallExpression", "span": {"start": 1928, "end": 1966}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 1928, "end": 1937}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 1938, "end": 1958}, "value": "app.update.enabled", "raw": "\"app.update.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 1960, "end": 1965}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 1968, "end": 2049}, "expression": {"type": "CallExpression", "span": {"start": 1968, "end": 2048}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 1968, "end": 1977}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 1978, "end": 2035}, "value": "app.update.lastUpdateTime.addon-background-update-timer", "raw": "\"app.update.lastUpdateTime.addon-background-update-timer\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2037, "end": 2047}, "value": 1747051837, "raw": "1747051837"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2050, "end": 2125}, "expression": {"type": "CallExpression", "span": {"start": 2050, "end": 2124}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2050, "end": 2059}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2060, "end": 2111}, "value": "app.update.lastUpdateTime.background-update-timer", "raw": "\"app.update.lastUpdateTime.background-update-timer\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2113, "end": 2123}, "value": 1747051837, "raw": "1747051837"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2126, "end": 2204}, "expression": {"type": "CallExpression", "span": {"start": 2126, "end": 2203}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2126, "end": 2135}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2136, "end": 2190}, "value": "app.update.lastUpdateTime.xpi-signature-verification", "raw": "\"app.update.lastUpdateTime.xpi-signature-verification\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2192, "end": 2202}, "value": 1747051837, "raw": "1747051837"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2205, "end": 2248}, "expression": {"type": "CallExpression", "span": {"start": 2205, "end": 2247}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2205, "end": 2214}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2215, "end": 2240}, "value": "browser.EULA.3.accepted", "raw": "\"browser.EULA.3.accepted\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2242, "end": 2246}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2249, "end": 2290}, "expression": {"type": "CallExpression", "span": {"start": 2249, "end": 2289}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2249, "end": 2258}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2259, "end": 2282}, "value": "browser.EULA.override", "raw": "\"browser.EULA.override\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2284, "end": 2288}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2291, "end": 2365}, "expression": {"type": "CallExpression", "span": {"start": 2291, "end": 2364}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2291, "end": 2300}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2301, "end": 2351}, "value": "browser.laterrun.bookkeeping.profileCreationTime", "raw": "\"browser.laterrun.bookkeeping.profileCreationTime\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2353, "end": 2363}, "value": 1737545030, "raw": "1737545030"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2366, "end": 2425}, "expression": {"type": "CallExpression", "span": {"start": 2366, "end": 2424}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2366, "end": 2375}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2376, "end": 2419}, "value": "browser.laterrun.bookkeeping.sessionCount", "raw": "\"browser.laterrun.bookkeeping.sessionCount\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2421, "end": 2423}, "value": 51, "raw": "51"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2426, "end": 2469}, "expression": {"type": "CallExpression", "span": {"start": 2426, "end": 2468}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2426, "end": 2435}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2436, "end": 2464}, "value": "browser.link.open_external", "raw": "\"browser.link.open_external\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 2466, "end": 2467}, "value": 2, "raw": "2"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2470, "end": 2506}, "expression": {"type": "CallExpression", "span": {"start": 2470, "end": 2505}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2470, "end": 2479}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2480, "end": 2497}, "value": "browser.offline", "raw": "\"browser.offline\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2499, "end": 2504}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2507, "end": 2562}, "expression": {"type": "CallExpression", "span": {"start": 2507, "end": 2561}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2507, "end": 2516}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2517, "end": 2554}, "value": "browser.reader.detectedFirstArticle", "raw": "\"browser.reader.detectedFirstArticle\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2556, "end": 2560}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2563, "end": 2612}, "expression": {"type": "CallExpression", "span": {"start": 2563, "end": 2611}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2563, "end": 2572}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2573, "end": 2603}, "value": "browser.safebrowsing.enabled", "raw": "\"browser.safebrowsing.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2605, "end": 2610}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2613, "end": 2670}, "expression": {"type": "CallExpression", "span": {"start": 2613, "end": 2669}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2613, "end": 2622}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2623, "end": 2661}, "value": "browser.safebrowsing.malware.enabled", "raw": "\"browser.safebrowsing.malware.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 2663, "end": 2668}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2671, "end": 2774}, "expression": {"type": "CallExpression", "span": {"start": 2671, "end": 2773}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2671, "end": 2680}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2681, "end": 2725}, "value": "browser.safebrowsing.provider.0.gethashURL", "raw": "\"browser.safebrowsing.provider.0.gethashURL\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2727, "end": 2772}, "value": "http://localhost/safebrowsing-dummy/gethash", "raw": "\"http://localhost/safebrowsing-dummy/gethash\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2775, "end": 2873}, "expression": {"type": "CallExpression", "span": {"start": 2775, "end": 2872}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2775, "end": 2784}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2785, "end": 2825}, "value": "browser.safebrowsing.provider.0.keyURL", "raw": "\"browser.safebrowsing.provider.0.keyURL\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2827, "end": 2871}, "value": "http://localhost/safebrowsing-dummy/newkey", "raw": "\"http://localhost/safebrowsing-dummy/newkey\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2874, "end": 2975}, "expression": {"type": "CallExpression", "span": {"start": 2874, "end": 2974}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2874, "end": 2883}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2884, "end": 2927}, "value": "browser.safebrowsing.provider.0.updateURL", "raw": "\"browser.safebrowsing.provider.0.updateURL\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2929, "end": 2973}, "value": "http://localhost/safebrowsing-dummy/update", "raw": "\"http://localhost/safebrowsing-dummy/update\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 2976, "end": 3018}, "expression": {"type": "CallExpression", "span": {"start": 2976, "end": 3017}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 2976, "end": 2985}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 2986, "end": 3009}, "value": "browser.search.update", "raw": "\"browser.search.update\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3011, "end": 3016}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3019, "end": 3088}, "expression": {"type": "CallExpression", "span": {"start": 3019, "end": 3087}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3019, "end": 3028}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3029, "end": 3054}, "value": "browser.selfsupport.url", "raw": "\"browser.selfsupport.url\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3056, "end": 3086}, "value": "https://localhost/selfrepair", "raw": "\"https://localhost/selfrepair\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3089, "end": 3148}, "expression": {"type": "CallExpression", "span": {"start": 3089, "end": 3147}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3089, "end": 3098}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3099, "end": 3139}, "value": "browser.sessionstore.resume_from_crash", "raw": "\"browser.sessionstore.resume_from_crash\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3141, "end": 3146}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3149, "end": 3203}, "expression": {"type": "CallExpression", "span": {"start": 3149, "end": 3202}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3149, "end": 3158}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3159, "end": 3194}, "value": "browser.shell.checkDefaultBrowser", "raw": "\"browser.shell.checkDefaultBrowser\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3196, "end": 3201}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3204, "end": 3257}, "expression": {"type": "CallExpression", "span": {"start": 3204, "end": 3256}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3204, "end": 3213}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3214, "end": 3240}, "value": "browser.startup.homepage", "raw": "\"browser.startup.homepage\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3242, "end": 3255}, "value": "about:blank", "raw": "\"about:blank\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3258, "end": 3331}, "expression": {"type": "CallExpression", "span": {"start": 3258, "end": 3330}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3258, "end": 3267}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3268, "end": 3311}, "value": "browser.startup.homepage_override.buildID", "raw": "\"browser.startup.homepage_override.buildID\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3313, "end": 3329}, "value": "20240418052210", "raw": "\"20240418052210\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3332, "end": 3398}, "expression": {"type": "CallExpression", "span": {"start": 3332, "end": 3397}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3332, "end": 3341}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3342, "end": 3384}, "value": "browser.startup.homepage_override.mstone", "raw": "\"browser.startup.homepage_override.mstone\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3386, "end": 3396}, "value": "115.10.0", "raw": "\"115.10.0\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3399, "end": 3436}, "expression": {"type": "CallExpression", "span": {"start": 3399, "end": 3435}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3399, "end": 3408}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3409, "end": 3431}, "value": "browser.startup.page", "raw": "\"browser.startup.page\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 3433, "end": 3434}, "value": 0, "raw": "0"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3437, "end": 3481}, "expression": {"type": "CallExpression", "span": {"start": 3437, "end": 3480}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3437, "end": 3446}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3447, "end": 3472}, "value": "browser.tabs.warnOnOpen", "raw": "\"browser.tabs.warnOnOpen\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3474, "end": 3479}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3482, "end": 3532}, "expression": {"type": "CallExpression", "span": {"start": 3482, "end": 3531}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3482, "end": 3491}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3492, "end": 3526}, "value": "datareporting.policy.firstRunURL", "raw": "\"datareporting.policy.firstRunURL\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3528, "end": 3530}, "value": "", "raw": "\"\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3533, "end": 3592}, "expression": {"type": "CallExpression", "span": {"start": 3533, "end": 3591}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3533, "end": 3542}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3543, "end": 3584}, "value": "devtools.browserconsole.contentMessages", "raw": "\"devtools.browserconsole.contentMessages\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3586, "end": 3590}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3593, "end": 3636}, "expression": {"type": "CallExpression", "span": {"start": 3593, "end": 3635}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3593, "end": 3602}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3603, "end": 3628}, "value": "devtools.chrome.enabled", "raw": "\"devtools.chrome.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3630, "end": 3634}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3637, "end": 3693}, "expression": {"type": "CallExpression", "span": {"start": 3637, "end": 3692}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3637, "end": 3646}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3647, "end": 3684}, "value": "devtools.debugger.prompt-connection", "raw": "\"devtools.debugger.prompt-connection\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3686, "end": 3691}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3694, "end": 3746}, "expression": {"type": "CallExpression", "span": {"start": 3694, "end": 3745}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3694, "end": 3703}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3704, "end": 3738}, "value": "devtools.debugger.remote-enabled", "raw": "\"devtools.debugger.remote-enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3740, "end": 3744}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3747, "end": 3801}, "expression": {"type": "CallExpression", "span": {"start": 3747, "end": 3800}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3747, "end": 3756}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3757, "end": 3793}, "value": "devtools.debugger.remote-websocket", "raw": "\"devtools.debugger.remote-websocket\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3795, "end": 3799}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3802, "end": 3851}, "expression": {"type": "CallExpression", "span": {"start": 3802, "end": 3850}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3802, "end": 3811}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3812, "end": 3843}, "value": "devtools.errorconsole.enabled", "raw": "\"devtools.errorconsole.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3845, "end": 3849}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3852, "end": 3901}, "expression": {"type": "CallExpression", "span": {"start": 3852, "end": 3900}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3852, "end": 3861}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3862, "end": 3892}, "value": "dom.disable_open_during_load", "raw": "\"dom.disable_open_during_load\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 3894, "end": 3899}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3902, "end": 3943}, "expression": {"type": "CallExpression", "span": {"start": 3902, "end": 3942}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3902, "end": 3911}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3912, "end": 3937}, "value": "dom.max_script_run_time", "raw": "\"dom.max_script_run_time\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 3939, "end": 3941}, "value": 30, "raw": "30"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3944, "end": 3990}, "expression": {"type": "CallExpression", "span": {"start": 3944, "end": 3989}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3944, "end": 3953}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 3954, "end": 3984}, "value": "extensions.autoDisableScopes", "raw": "\"extensions.autoDisableScopes\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 3986, "end": 3988}, "value": 10, "raw": "10"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 3991, "end": 4045}, "expression": {"type": "CallExpression", "span": {"start": 3991, "end": 4044}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 3991, "end": 4000}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4001, "end": 4040}, "value": "extensions.blocklist.pingCountVersion", "raw": "\"extensions.blocklist.pingCountVersion\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 4042, "end": 4043}, "value": 0, "raw": "0"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4046, "end": 4104}, "expression": {"type": "CallExpression", "span": {"start": 4046, "end": 4103}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4046, "end": 4055}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4056, "end": 4095}, "value": "extensions.checkCompatibility.nightly", "raw": "\"extensions.checkCompatibility.nightly\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4097, "end": 4102}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4105, "end": 4148}, "expression": {"type": "CallExpression", "span": {"start": 4105, "end": 4147}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4105, "end": 4114}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4115, "end": 4142}, "value": "extensions.databaseSchema", "raw": "\"extensions.databaseSchema\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 4144, "end": 4146}, "value": 35, "raw": "35"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4149, "end": 4190}, "expression": {"type": "CallExpression", "span": {"start": 4149, "end": 4189}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4149, "end": 4158}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4159, "end": 4185}, "value": "extensions.enabledScopes", "raw": "\"extensions.enabledScopes\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 4187, "end": 4188}, "value": 5, "raw": "5"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4191, "end": 4242}, "expression": {"type": "CallExpression", "span": {"start": 4191, "end": 4241}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4191, "end": 4200}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4201, "end": 4233}, "value": "extensions.installDistroAddons", "raw": "\"extensions.installDistroAddons\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4235, "end": 4240}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4243, "end": 4300}, "expression": {"type": "CallExpression", "span": {"start": 4243, "end": 4299}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4243, "end": 4252}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4253, "end": 4280}, "value": "extensions.lastAppBuildId", "raw": "\"extensions.lastAppBuildId\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4282, "end": 4298}, "value": "20241206070755", "raw": "\"20241206070755\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4301, "end": 4350}, "expression": {"type": "CallExpression", "span": {"start": 4301, "end": 4349}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4301, "end": 4310}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4311, "end": 4338}, "value": "extensions.lastAppVersion", "raw": "\"extensions.lastAppVersion\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4340, "end": 4348}, "value": "7.0.11", "raw": "\"7.0.11\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4351, "end": 4407}, "expression": {"type": "CallExpression", "span": {"start": 4351, "end": 4406}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4351, "end": 4360}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4361, "end": 4393}, "value": "extensions.lastPlatformVersion", "raw": "\"extensions.lastPlatformVersion\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4395, "end": 4405}, "value": "115.10.0", "raw": "\"115.10.0\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4408, "end": 4479}, "expression": {"type": "CallExpression", "span": {"start": 4408, "end": 4478}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4408, "end": 4417}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4418, "end": 4445}, "value": "extensions.systemAddonSet", "raw": "\"extensions.systemAddonSet\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4447, "end": 4477}, "value": "{\"schema\":1,\"addons\":{}}", "raw": "\"{\\\"schema\\\":1,\\\"addons\\\":{}}\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4480, "end": 4531}, "expression": {"type": "CallExpression", "span": {"start": 4480, "end": 4530}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4480, "end": 4489}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4490, "end": 4523}, "value": "extensions.ui.dictionary.hidden", "raw": "\"extensions.ui.dictionary.hidden\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4525, "end": 4529}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4532, "end": 4579}, "expression": {"type": "CallExpression", "span": {"start": 4532, "end": 4578}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4532, "end": 4541}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4542, "end": 4571}, "value": "extensions.ui.locale.hidden", "raw": "\"extensions.ui.locale.hidden\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4573, "end": 4577}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4580, "end": 4626}, "expression": {"type": "CallExpression", "span": {"start": 4580, "end": 4625}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4580, "end": 4589}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4590, "end": 4617}, "value": "extensions.update.enabled", "raw": "\"extensions.update.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4619, "end": 4624}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4627, "end": 4676}, "expression": {"type": "CallExpression", "span": {"start": 4627, "end": 4675}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4627, "end": 4636}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4637, "end": 4667}, "value": "extensions.update.notifyUser", "raw": "\"extensions.update.notifyUser\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4669, "end": 4674}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4677, "end": 4796}, "expression": {"type": "CallExpression", "span": {"start": 4677, "end": 4795}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4677, "end": 4686}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4687, "end": 4719}, "value": "extensions.webextensions.uuids", "raw": "\"extensions.webextensions.uuids\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4721, "end": 4794}, "value": "{\"<EMAIL>\":\"7ef5bab7-1fd0-4373-9450-c398a293855a\"}", "raw": "\"{\\\"<EMAIL>\\\":\\\"7ef5bab7-1fd0-4373-9450-c398a293855a\\\"}\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4797, "end": 4872}, "expression": {"type": "CallExpression", "span": {"start": 4797, "end": 4871}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4797, "end": 4806}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4807, "end": 4834}, "value": "extensions.zotero.dataDir", "raw": "\"extensions.zotero.dataDir\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4836, "end": 4870}, "value": "/Users/<USER>/Zotero pdf2zh", "raw": "\"/Users/<USER>/Zotero pdf2zh\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4873, "end": 4949}, "expression": {"type": "CallExpression", "span": {"start": 4873, "end": 4948}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4873, "end": 4882}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4883, "end": 4941}, "value": "extensions.zotero.firstRun.skipFirefoxProfileAccessCheck", "raw": "\"extensions.zotero.firstRun.skipFirefoxProfileAccessCheck\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4943, "end": 4947}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4950, "end": 4998}, "expression": {"type": "CallExpression", "span": {"start": 4950, "end": 4997}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4950, "end": 4959}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 4960, "end": 4989}, "value": "extensions.zotero.firstRun2", "raw": "\"extensions.zotero.firstRun2\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 4991, "end": 4996}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 4999, "end": 5054}, "expression": {"type": "CallExpression", "span": {"start": 4999, "end": 5053}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 4999, "end": 5008}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5009, "end": 5045}, "value": "extensions.zotero.firstRunGuidance", "raw": "\"extensions.zotero.firstRunGuidance\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 5047, "end": 5052}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5055, "end": 5120}, "expression": {"type": "CallExpression", "span": {"start": 5055, "end": 5119}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5055, "end": 5064}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5065, "end": 5112}, "value": "extensions.zotero.httpServer.localAPI.enabled", "raw": "\"extensions.zotero.httpServer.localAPI.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 5114, "end": 5118}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5121, "end": 5175}, "expression": {"type": "CallExpression", "span": {"start": 5121, "end": 5174}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5121, "end": 5130}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5131, "end": 5166}, "value": "extensions.zotero.httpServer.port", "raw": "\"extensions.zotero.httpServer.port\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 5168, "end": 5173}, "value": 23124, "raw": "23124"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5176, "end": 5277}, "expression": {"type": "CallExpression", "span": {"start": 5176, "end": 5276}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5176, "end": 5185}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5186, "end": 5226}, "value": "extensions.zotero.lastSelectedPrefPane", "raw": "\"extensions.zotero.lastSelectedPrefPane\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5228, "end": 5275}, "value": "<EMAIL>", "raw": "\"<EMAIL>\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5278, "end": 5332}, "expression": {"type": "CallExpression", "span": {"start": 5278, "end": 5331}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5278, "end": 5287}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5288, "end": 5324}, "value": "extensions.zotero.lastViewedFolder", "raw": "\"extensions.zotero.lastViewedFolder\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5326, "end": 5330}, "value": "C1", "raw": "\"C1\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5333, "end": 5786}, "expression": {"type": "CallExpression", "span": {"start": 5333, "end": 5785}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5333, "end": 5342}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5343, "end": 5375}, "value": "extensions.zotero.pane.persist", "raw": "\"extensions.zotero.pane.persist\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5377, "end": 5784}, "value": "{\"zotero-reader-sidebar-pane\":{\"collapsed\":\"true\"},\"zotero-layout-switcher\":{\"orient\":\"horizontal\"},\"zotero-items-splitter\":{\"state\":\"\",\"orient\":\"horizontal\"},\"zotero-context-splitter\":{\"state\":\"collapsed\"},\"zotero-context-splitter-stacked\":{\"state\":\"collapsed\"},\"zotero-collections-pane\":{\"width\":\"212\"},\"zotero-item-pane\":{\"width\":\"623\",\"height\":\"205\"}}", "raw": "\"{\\\"zotero-reader-sidebar-pane\\\":{\\\"collapsed\\\":\\\"true\\\"},\\\"zotero-layout-switcher\\\":{\\\"orient\\\":\\\"horizontal\\\"},\\\"zotero-items-splitter\\\":{\\\"state\\\":\\\"\\\",\\\"orient\\\":\\\"horizontal\\\"},\\\"zotero-context-splitter\\\":{\\\"state\\\":\\\"collapsed\\\"},\\\"zotero-context-splitter-stacked\\\":{\\\"state\\\":\\\"collapsed\\\"},\\\"zotero-collections-pane\\\":{\\\"width\\\":\\\"212\\\"},\\\"zotero-item-pane\\\":{\\\"width\\\":\\\"623\\\",\\\"height\\\":\\\"205\\\"}}\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5787, "end": 5845}, "expression": {"type": "CallExpression", "span": {"start": 5787, "end": 5844}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5787, "end": 5796}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5797, "end": 5836}, "value": "extensions.zotero.panes.abstract.open", "raw": "\"extensions.zotero.panes.abstract.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 5838, "end": 5843}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5846, "end": 5917}, "expression": {"type": "CallExpression", "span": {"start": 5846, "end": 5916}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5846, "end": 5855}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5856, "end": 5909}, "value": "extensions.zotero.panes.attachment-annotations.open", "raw": "\"extensions.zotero.panes.attachment-annotations.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 5911, "end": 5915}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5918, "end": 5982}, "expression": {"type": "CallExpression", "span": {"start": 5918, "end": 5981}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5918, "end": 5927}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5928, "end": 5974}, "value": "extensions.zotero.panes.attachment-info.open", "raw": "\"extensions.zotero.panes.attachment-info.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 5976, "end": 5980}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 5983, "end": 6043}, "expression": {"type": "CallExpression", "span": {"start": 5983, "end": 6042}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 5983, "end": 5992}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 5993, "end": 6035}, "value": "extensions.zotero.panes.attachments.open", "raw": "\"extensions.zotero.panes.attachments.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6037, "end": 6041}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6044, "end": 6110}, "expression": {"type": "CallExpression", "span": {"start": 6044, "end": 6109}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6044, "end": 6053}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6054, "end": 6102}, "value": "extensions.zotero.panes.context-all-notes.open", "raw": "\"extensions.zotero.panes.context-all-notes.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6104, "end": 6108}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6111, "end": 6178}, "expression": {"type": "CallExpression", "span": {"start": 6111, "end": 6177}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6111, "end": 6120}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6121, "end": 6170}, "value": "extensions.zotero.panes.context-item-notes.open", "raw": "\"extensions.zotero.panes.context-item-notes.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6172, "end": 6176}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6179, "end": 6232}, "expression": {"type": "CallExpression", "span": {"start": 6179, "end": 6231}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6179, "end": 6188}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6189, "end": 6224}, "value": "extensions.zotero.panes.info.open", "raw": "\"extensions.zotero.panes.info.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6226, "end": 6230}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6233, "end": 6303}, "expression": {"type": "CallExpression", "span": {"start": 6233, "end": 6302}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6233, "end": 6242}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6243, "end": 6295}, "value": "extensions.zotero.panes.libraries-collections.open", "raw": "\"extensions.zotero.panes.libraries-collections.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6297, "end": 6301}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6304, "end": 6358}, "expression": {"type": "CallExpression", "span": {"start": 6304, "end": 6357}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6304, "end": 6313}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6314, "end": 6350}, "value": "extensions.zotero.panes.notes.open", "raw": "\"extensions.zotero.panes.notes.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6352, "end": 6356}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6359, "end": 6415}, "expression": {"type": "CallExpression", "span": {"start": 6359, "end": 6414}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6359, "end": 6368}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6369, "end": 6407}, "value": "extensions.zotero.panes.related.open", "raw": "\"extensions.zotero.panes.related.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6409, "end": 6413}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6416, "end": 6469}, "expression": {"type": "CallExpression", "span": {"start": 6416, "end": 6468}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6416, "end": 6425}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6426, "end": 6461}, "value": "extensions.zotero.panes.tags.open", "raw": "\"extensions.zotero.panes.tags.open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6463, "end": 6467}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6470, "end": 6522}, "expression": {"type": "CallExpression", "span": {"start": 6470, "end": 6521}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6470, "end": 6479}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6480, "end": 6514}, "value": "extensions.zotero.pdf2zh.compare", "raw": "\"extensions.zotero.pdf2zh.compare\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6516, "end": 6520}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6523, "end": 6580}, "expression": {"type": "CallExpression", "span": {"start": 6523, "end": 6579}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6523, "end": 6532}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6533, "end": 6572}, "value": "extensions.zotero.pdf2zh.compare-open", "raw": "\"extensions.zotero.pdf2zh.compare-open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6574, "end": 6578}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6581, "end": 6634}, "expression": {"type": "CallExpression", "span": {"start": 6581, "end": 6633}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6581, "end": 6590}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6591, "end": 6626}, "value": "extensions.zotero.pdf2zh.dual-cut", "raw": "\"extensions.zotero.pdf2zh.dual-cut\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6628, "end": 6632}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6635, "end": 6689}, "expression": {"type": "CallExpression", "span": {"start": 6635, "end": 6688}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6635, "end": 6644}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6645, "end": 6680}, "value": "extensions.zotero.pdf2zh.dual_cut", "raw": "\"extensions.zotero.pdf2zh.dual_cut\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6682, "end": 6687}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6690, "end": 6743}, "expression": {"type": "CallExpression", "span": {"start": 6690, "end": 6742}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6690, "end": 6699}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6700, "end": 6735}, "value": "extensions.zotero.pdf2zh.mono-cut", "raw": "\"extensions.zotero.pdf2zh.mono-cut\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6737, "end": 6741}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6744, "end": 6798}, "expression": {"type": "CallExpression", "span": {"start": 6744, "end": 6797}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6744, "end": 6753}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6754, "end": 6789}, "value": "extensions.zotero.pdf2zh.mono_cut", "raw": "\"extensions.zotero.pdf2zh.mono_cut\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 6791, "end": 6796}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6799, "end": 6854}, "expression": {"type": "CallExpression", "span": {"start": 6799, "end": 6853}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6799, "end": 6808}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6809, "end": 6842}, "value": "extensions.zotero.pdf2zh.preset", "raw": "\"extensions.zotero.pdf2zh.preset\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6844, "end": 6852}, "value": "google", "raw": "\"google\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6855, "end": 6908}, "expression": {"type": "CallExpression", "span": {"start": 6855, "end": 6907}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6855, "end": 6864}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6865, "end": 6902}, "value": "extensions.zotero.pdf2zh.pythoncode", "raw": "\"extensions.zotero.pdf2zh.pythoncode\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6904, "end": 6906}, "value": "", "raw": "\"\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6909, "end": 6970}, "expression": {"type": "CallExpression", "span": {"start": 6909, "end": 6969}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6909, "end": 6918}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6919, "end": 6953}, "value": "extensions.zotero.pdf2zh.service", "raw": "\"extensions.zotero.pdf2zh.service\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6955, "end": 6968}, "value": "openailiked", "raw": "\"openailiked\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 6971, "end": 7038}, "expression": {"type": "CallExpression", "span": {"start": 6971, "end": 7037}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 6971, "end": 6980}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 6981, "end": 7021}, "value": "extensions.zotero.pdf2zh.serviceselect", "raw": "\"extensions.zotero.pdf2zh.serviceselect\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7023, "end": 7036}, "value": "openailiked", "raw": "\"openailiked\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7039, "end": 7098}, "expression": {"type": "CallExpression", "span": {"start": 7039, "end": 7097}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7039, "end": 7048}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7049, "end": 7090}, "value": "extensions.zotero.pdf2zh.single-compare", "raw": "\"extensions.zotero.pdf2zh.single-compare\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7092, "end": 7096}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7099, "end": 7163}, "expression": {"type": "CallExpression", "span": {"start": 7099, "end": 7162}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7099, "end": 7108}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7109, "end": 7155}, "value": "extensions.zotero.pdf2zh.single-compare-open", "raw": "\"extensions.zotero.pdf2zh.single-compare-open\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7157, "end": 7161}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7164, "end": 7223}, "expression": {"type": "CallExpression", "span": {"start": 7164, "end": 7222}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7164, "end": 7173}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7174, "end": 7215}, "value": "extensions.zotero.pdf2zh.single_compare", "raw": "\"extensions.zotero.pdf2zh.single_compare\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7217, "end": 7221}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7224, "end": 7281}, "expression": {"type": "CallExpression", "span": {"start": 7224, "end": 7280}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7224, "end": 7233}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7234, "end": 7276}, "value": "extensions.zotero.pdf2zh.skip-last-pages", "raw": "\"extensions.zotero.pdf2zh.skip-last-pages\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 7278, "end": 7279}, "value": 1, "raw": "1"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7282, "end": 7334}, "expression": {"type": "CallExpression", "span": {"start": 7282, "end": 7333}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7282, "end": 7291}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7292, "end": 7328}, "value": "extensions.zotero.pdf2zh.threadNum", "raw": "\"extensions.zotero.pdf2zh.threadNum\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 7330, "end": 7332}, "value": 16, "raw": "16"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7335, "end": 7386}, "expression": {"type": "CallExpression", "span": {"start": 7335, "end": 7385}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7335, "end": 7344}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7345, "end": 7381}, "value": "extensions.zotero.pdf2zh.threadnum", "raw": "\"extensions.zotero.pdf2zh.threadnum\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 7383, "end": 7384}, "value": 8, "raw": "8"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7387, "end": 7434}, "expression": {"type": "CallExpression", "span": {"start": 7387, "end": 7433}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7387, "end": 7396}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7397, "end": 7428}, "value": "extensions.zotero.prefVersion", "raw": "\"extensions.zotero.prefVersion\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 7430, "end": 7432}, "value": 13, "raw": "13"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7435, "end": 7483}, "expression": {"type": "CallExpression", "span": {"start": 7435, "end": 7482}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7435, "end": 7444}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7445, "end": 7475}, "value": "extensions.zotero.purge.tags", "raw": "\"extensions.zotero.purge.tags\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7477, "end": 7481}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7484, "end": 7563}, "expression": {"type": "CallExpression", "span": {"start": 7484, "end": 7562}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7484, "end": 7493}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7494, "end": 7548}, "value": "extensions.zotero.reader.textSelectionAnnotationMode", "raw": "\"extensions.zotero.reader.textSelectionAnnotationMode\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7550, "end": 7561}, "value": "underline", "raw": "\"underline\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7564, "end": 7661}, "expression": {"type": "CallExpression", "span": {"start": 7564, "end": 7660}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7564, "end": 7573}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7574, "end": 7611}, "value": "extensions.zotero.recentSaveTargets", "raw": "\"extensions.zotero.recentSaveTargets\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7613, "end": 7659}, "value": "[{\"id\":\"C1\",\"sessionID\":\"Mfby255X\"}]", "raw": "\"[{\\\"id\\\":\\\"C1\\\",\\\"sessionID\\\":\\\"Mfby255X\\\"}]\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7662, "end": 7725}, "expression": {"type": "CallExpression", "span": {"start": 7662, "end": 7724}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7662, "end": 7671}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7672, "end": 7716}, "value": "extensions.zotero.reportTranslationFailure", "raw": "\"extensions.zotero.reportTranslationFailure\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7718, "end": 7723}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7726, "end": 7792}, "expression": {"type": "CallExpression", "span": {"start": 7726, "end": 7791}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7726, "end": 7735}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7736, "end": 7783}, "value": "extensions.zotero.sync.reminder.setUp.enabled", "raw": "\"extensions.zotero.sync.reminder.setUp.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7785, "end": 7790}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7793, "end": 7870}, "expression": {"type": "CallExpression", "span": {"start": 7793, "end": 7869}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7793, "end": 7802}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7803, "end": 7856}, "value": "extensions.zotero.sync.reminder.setUp.lastDisplayed", "raw": "\"extensions.zotero.sync.reminder.setUp.lastDisplayed\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 7858, "end": 7868}, "value": 1737545072, "raw": "1737545072"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7871, "end": 7919}, "expression": {"type": "CallExpression", "span": {"start": 7871, "end": 7918}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7871, "end": 7880}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7881, "end": 7911}, "value": "extensions.zotero.useDataDir", "raw": "\"extensions.zotero.useDataDir\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7913, "end": 7917}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7920, "end": 7985}, "expression": {"type": "CallExpression", "span": {"start": 7920, "end": 7984}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7920, "end": 7929}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7930, "end": 7977}, "value": "extensions.zoteroMacWordIntegration.installed", "raw": "\"extensions.zoteroMacWordIntegration.installed\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 7979, "end": 7983}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 7986, "end": 8072}, "expression": {"type": "CallExpression", "span": {"start": 7986, "end": 8071}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 7986, "end": 7995}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 7996, "end": 8054}, "value": "extensions.zoteroMacWordIntegration.lastAttemptedVersion", "raw": "\"extensions.zoteroMacWordIntegration.lastAttemptedVersion\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8056, "end": 8070}, "value": "7.0.6.SOURCE", "raw": "\"7.0.6.SOURCE\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8073, "end": 8145}, "expression": {"type": "CallExpression", "span": {"start": 8073, "end": 8144}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8073, "end": 8082}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8083, "end": 8137}, "value": "extensions.zoteroMacWordIntegration.skipInstallation", "raw": "\"extensions.zoteroMacWordIntegration.skipInstallation\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 8139, "end": 8143}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8146, "end": 8219}, "expression": {"type": "CallExpression", "span": {"start": 8146, "end": 8218}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8146, "end": 8155}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8156, "end": 8201}, "value": "extensions.zoteroMacWordIntegration.version", "raw": "\"extensions.zoteroMacWordIntegration.version\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8203, "end": 8217}, "value": "7.0.6.SOURCE", "raw": "\"7.0.6.SOURCE\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8220, "end": 8309}, "expression": {"type": "CallExpression", "span": {"start": 8220, "end": 8308}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8220, "end": 8229}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8230, "end": 8291}, "value": "extensions.zoteroOpenOfficeIntegration.lastAttemptedVersion", "raw": "\"extensions.zoteroOpenOfficeIntegration.lastAttemptedVersion\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8293, "end": 8307}, "value": "7.0.1.SOURCE", "raw": "\"7.0.1.SOURCE\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8310, "end": 8385}, "expression": {"type": "CallExpression", "span": {"start": 8310, "end": 8384}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8310, "end": 8319}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8320, "end": 8377}, "value": "extensions.zoteroOpenOfficeIntegration.skipInstallation", "raw": "\"extensions.zoteroOpenOfficeIntegration.skipInstallation\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 8379, "end": 8383}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8386, "end": 8458}, "expression": {"type": "CallExpression", "span": {"start": 8386, "end": 8457}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8386, "end": 8395}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8396, "end": 8450}, "value": "extensions.zoteroWinWordIntegration.skipInstallation", "raw": "\"extensions.zoteroWinWordIntegration.skipInstallation\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 8452, "end": 8456}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8459, "end": 8519}, "expression": {"type": "CallExpression", "span": {"start": 8459, "end": 8518}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8459, "end": 8468}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8469, "end": 8514}, "value": "gecko.handlerService.defaultHandlersVersion", "raw": "\"gecko.handlerService.defaultHandlersVersion\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 8516, "end": 8517}, "value": 1, "raw": "1"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8520, "end": 8572}, "expression": {"type": "CallExpression", "span": {"start": 8520, "end": 8571}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8520, "end": 8529}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8530, "end": 8558}, "value": "idle.lastDailyNotification", "raw": "\"idle.lastDailyNotification\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 8560, "end": 8570}, "value": 1746613004, "raw": "1746613004"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8573, "end": 8646}, "expression": {"type": "CallExpression", "span": {"start": 8573, "end": 8645}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8573, "end": 8582}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8583, "end": 8606}, "value": "intl.accept_languages", "raw": "\"intl.accept_languages\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8608, "end": 8644}, "value": "zh-CN, zh, zh-TW, zh-HK, en-US, en", "raw": "\"zh-CN, zh, zh-TW, zh-HK, en-US, en\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8647, "end": 8698}, "expression": {"type": "CallExpression", "span": {"start": 8647, "end": 8697}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8647, "end": 8656}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8657, "end": 8693}, "value": "media.gmp.storage.version.observed", "raw": "\"media.gmp.storage.version.observed\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 8695, "end": 8696}, "value": 1, "raw": "1"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8699, "end": 8756}, "expression": {"type": "CallExpression", "span": {"start": 8699, "end": 8755}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8699, "end": 8708}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8709, "end": 8750}, "value": "network.http.max-connections-per-server", "raw": "\"network.http.max-connections-per-server\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 8752, "end": 8754}, "value": 10, "raw": "10"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8757, "end": 8811}, "expression": {"type": "CallExpression", "span": {"start": 8757, "end": 8810}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8757, "end": 8766}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8767, "end": 8804}, "value": "network.http.phishy-userpass-length", "raw": "\"network.http.phishy-userpass-length\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 8806, "end": 8809}, "value": 255, "raw": "255"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8812, "end": 8861}, "expression": {"type": "CallExpression", "span": {"start": 8812, "end": 8860}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8812, "end": 8821}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8822, "end": 8853}, "value": "offline-apps.allow_by_default", "raw": "\"offline-apps.allow_by_default\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 8855, "end": 8859}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8862, "end": 8927}, "expression": {"type": "CallExpression", "span": {"start": 8862, "end": 8926}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8862, "end": 8871}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8872, "end": 8920}, "value": "privacy.purge_trackers.date_in_cookie_database", "raw": "\"privacy.purge_trackers.date_in_cookie_database\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8922, "end": 8925}, "value": "0", "raw": "\"0\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8928, "end": 8992}, "expression": {"type": "CallExpression", "span": {"start": 8928, "end": 8991}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8928, "end": 8937}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8938, "end": 8973}, "value": "privacy.purge_trackers.last_purge", "raw": "\"privacy.purge_trackers.last_purge\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 8975, "end": 8990}, "value": "1746613004497", "raw": "\"1746613004497\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 8993, "end": 9039}, "expression": {"type": "CallExpression", "span": {"start": 8993, "end": 9038}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 8993, "end": 9002}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9003, "end": 9030}, "value": "prompts.tab_modal.enabled", "raw": "\"prompts.tab_modal.enabled\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9032, "end": 9037}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9040, "end": 9087}, "expression": {"type": "CallExpression", "span": {"start": 9040, "end": 9086}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9040, "end": 9049}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9050, "end": 9082}, "value": "security.fileuri.origin_policy", "raw": "\"security.fileuri.origin_policy\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 9084, "end": 9085}, "value": 3, "raw": "3"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9088, "end": 9146}, "expression": {"type": "CallExpression", "span": {"start": 9088, "end": 9145}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9088, "end": 9097}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9098, "end": 9137}, "value": "security.fileuri.strict_origin_policy", "raw": "\"security.fileuri.strict_origin_policy\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9139, "end": 9144}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9147, "end": 9197}, "expression": {"type": "CallExpression", "span": {"start": 9147, "end": 9196}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9147, "end": 9156}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9157, "end": 9188}, "value": "security.warn_entering_secure", "raw": "\"security.warn_entering_secure\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9190, "end": 9195}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9198, "end": 9258}, "expression": {"type": "CallExpression", "span": {"start": 9198, "end": 9257}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9198, "end": 9207}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9208, "end": 9249}, "value": "security.warn_entering_secure.show_once", "raw": "\"security.warn_entering_secure.show_once\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9251, "end": 9256}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9259, "end": 9307}, "expression": {"type": "CallExpression", "span": {"start": 9259, "end": 9306}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9259, "end": 9268}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9269, "end": 9298}, "value": "security.warn_entering_weak", "raw": "\"security.warn_entering_weak\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9300, "end": 9305}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9308, "end": 9366}, "expression": {"type": "CallExpression", "span": {"start": 9308, "end": 9365}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9308, "end": 9317}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9318, "end": 9357}, "value": "security.warn_entering_weak.show_once", "raw": "\"security.warn_entering_weak.show_once\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9359, "end": 9364}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9367, "end": 9416}, "expression": {"type": "CallExpression", "span": {"start": 9367, "end": 9415}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9367, "end": 9376}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9377, "end": 9407}, "value": "security.warn_leaving_secure", "raw": "\"security.warn_leaving_secure\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9409, "end": 9414}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9417, "end": 9476}, "expression": {"type": "CallExpression", "span": {"start": 9417, "end": 9475}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9417, "end": 9426}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9427, "end": 9467}, "value": "security.warn_leaving_secure.show_once", "raw": "\"security.warn_leaving_secure.show_once\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9469, "end": 9474}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9477, "end": 9527}, "expression": {"type": "CallExpression", "span": {"start": 9477, "end": 9526}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9477, "end": 9486}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9487, "end": 9518}, "value": "security.warn_submit_insecure", "raw": "\"security.warn_submit_insecure\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9520, "end": 9525}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9528, "end": 9586}, "expression": {"type": "CallExpression", "span": {"start": 9528, "end": 9585}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9528, "end": 9537}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9538, "end": 9577}, "value": "security.warn_viewing_mixed.show_once", "raw": "\"security.warn_viewing_mixed.show_once\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9579, "end": 9584}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9587, "end": 9630}, "expression": {"type": "CallExpression", "span": {"start": 9587, "end": 9629}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9587, "end": 9596}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9597, "end": 9621}, "value": "signon.rememberSignons", "raw": "\"signon.rememberSignons\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9623, "end": 9628}, "value": false}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9631, "end": 9688}, "expression": {"type": "CallExpression", "span": {"start": 9631, "end": 9687}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9631, "end": 9640}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9641, "end": 9671}, "value": "startup.homepage_welcome_url", "raw": "\"startup.homepage_welcome_url\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9673, "end": 9686}, "value": "about:blank", "raw": "\"about:blank\""}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9689, "end": 9731}, "expression": {"type": "CallExpression", "span": {"start": 9689, "end": 9730}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9689, "end": 9698}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9699, "end": 9726}, "value": "storage.vacuum.last.index", "raw": "\"storage.vacuum.last.index\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 9728, "end": 9729}, "value": 0, "raw": "0"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9732, "end": 9791}, "expression": {"type": "CallExpression", "span": {"start": 9732, "end": 9790}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9732, "end": 9741}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9742, "end": 9777}, "value": "storage.vacuum.last.places.sqlite", "raw": "\"storage.vacuum.last.places.sqlite\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 9779, "end": 9789}, "value": 1746613004, "raw": "1746613004"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9792, "end": 9842}, "expression": {"type": "CallExpression", "span": {"start": 9792, "end": 9841}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9792, "end": 9801}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9802, "end": 9834}, "value": "toolkit.networkmanager.disable", "raw": "\"toolkit.networkmanager.disable\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9836, "end": 9840}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9843, "end": 9897}, "expression": {"type": "CallExpression", "span": {"start": 9843, "end": 9896}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9843, "end": 9852}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9853, "end": 9883}, "value": "toolkit.startup.last_success", "raw": "\"toolkit.startup.last_success\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 9885, "end": 9895}, "value": 1743342693, "raw": "1743342693"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9898, "end": 9941}, "expression": {"type": "CallExpression", "span": {"start": 9898, "end": 9940}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9898, "end": 9907}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9908, "end": 9936}, "value": "toolkit.telemetry.prompted", "raw": "\"toolkit.telemetry.prompted\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 9938, "end": 9939}, "value": 2, "raw": "2"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9942, "end": 9988}, "expression": {"type": "CallExpression", "span": {"start": 9942, "end": 9987}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9942, "end": 9951}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9952, "end": 9980}, "value": "toolkit.telemetry.rejected", "raw": "\"toolkit.telemetry.rejected\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 9982, "end": 9986}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 9989, "end": 10039}, "expression": {"type": "CallExpression", "span": {"start": 9989, "end": 10038}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 9989, "end": 9998}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 9999, "end": 10029}, "value": "urlclassifier.updateinterval", "raw": "\"urlclassifier.updateinterval\""}}, {"spread": null, "expression": {"type": "NumericLiteral", "span": {"start": 10031, "end": 10037}, "value": 172800, "raw": "172800"}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 10040, "end": 10092}, "expression": {"type": "CallExpression", "span": {"start": 10040, "end": 10091}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 10040, "end": 10049}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 10050, "end": 10084}, "value": "webdriver_accept_untrusted_certs", "raw": "\"webdriver_accept_untrusted_certs\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 10086, "end": 10090}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 10093, "end": 10146}, "expression": {"type": "CallExpression", "span": {"start": 10093, "end": 10145}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 10093, "end": 10102}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 10103, "end": 10138}, "value": "webdriver_assume_untrusted_issuer", "raw": "\"webdriver_assume_untrusted_issuer\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 10140, "end": 10144}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 10147, "end": 10197}, "expression": {"type": "CallExpression", "span": {"start": 10147, "end": 10196}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 10147, "end": 10156}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 10157, "end": 10189}, "value": "webdriver_enable_native_events", "raw": "\"webdriver_enable_native_events\""}}, {"spread": null, "expression": {"type": "<PERSON>olean<PERSON>iter<PERSON>", "span": {"start": 10191, "end": 10195}, "value": true}}], "typeArguments": null}}, {"type": "ExpressionStatement", "span": {"start": 10198, "end": 10253}, "expression": {"type": "CallExpression", "span": {"start": 10198, "end": 10252}, "ctxt": 0, "callee": {"type": "Identifier", "span": {"start": 10198, "end": 10207}, "ctxt": 1, "value": "user_pref", "optional": false}, "arguments": [{"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 10208, "end": 10239}, "value": "zotero-prefpane-pdf2zh.preset", "raw": "\"zotero-prefpane-pdf2zh.preset\""}}, {"spread": null, "expression": {"type": "StringLiteral", "span": {"start": 10241, "end": 10251}, "value": "deepseek", "raw": "\"deepseek\""}}], "typeArguments": null}}], "interpreter": null}