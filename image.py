image.pngimport tensorflow as tf
import numpy as np
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Dense
from tensorflow.keras.optimizers import Adam
import datetime

# 创建一些模拟数据
x_train = np.random.rand(1000, 20)
y_train = np.random.randint(0, 2, (1000, 1))

x_val = np.random.rand(200, 20)
y_val = np.random.randint(0, 2, (200, 1))

# 创建一个简单的模型
model = Sequential([
    Dense(64, activation='relu', input_shape=(20,)),
    Dense(32, activation='relu'),
    Dense(1, activation='sigmoid')
])

model.compile(
    optimizer=Adam(learning_rate=0.001),
    loss='binary_crossentropy',
    metrics=['accuracy']
)

# 设置TensorBoard回调
log_dir = "logs/fit/" + datetime.datetime.now().strftime("%Y%m%d-%H%M%S")
tensorboard_callback = tf.keras.callbacks.TensorBoard(
    log_dir=log_dir,
    histogram_freq=1,  # 每个epoch后记录激活和权重直方图
    write_graph=True,  # 记录模型图
    write_images=True,  # 记录模型权重为图像
    update_freq='epoch',  # 每个epoch更新一次
    profile_batch=2,  # 分析第二个batch
)

# 自定义回调来记录更多指标
class CustomCallback(tf.keras.callbacks.Callback):
    def __init__(self, log_dir):
        super(CustomCallback, self).__init__()
        self.log_dir = log_dir
        self.writer = tf.summary.create_file_writer(self.log_dir + '/custom_metrics')
        
    def on_epoch_end(self, epoch, logs=None):
        with self.writer.as_default():
            # 记录自定义指标
            tf.summary.scalar('learning_rate', self.model.optimizer.lr.numpy(), step=epoch)
            
            # 记录一些随机生成的指标作为示例
            tf.summary.scalar('custom_metric_1', np.random.rand(), step=epoch)
            tf.summary.scalar('custom_metric_2', np.random.rand(), step=epoch)
            
            # 记录一个图像
            random_image = np.random.rand(100, 100, 3)
            tf.summary.image('random_image', [random_image], step=epoch)

# 实例化自定义回调
custom_callback = CustomCallback(log_dir)

# 训练模型
history = model.fit(
    x_train, y_train,
    epochs=10,
    batch_size=32,
    validation_data=(x_val, y_val),
    callbacks=[tensorboard_callback, custom_callback]
)

# 保存模型
model.save(log_dir + '/model')

print(f"\nTensorBoard 日志保存在: {log_dir}")
print("运行以下命令启动TensorBoard:")
print(f"tensorboard --logdir={log_dir}")